// Client-side Whop app configuration detection and management

// Cache for experience app names to avoid repeated API calls
const experienceAppCache = new Map<string, string>();

interface WhopAppConfig {
  appId: string;
  agentUserId: string;
  companyId: string;
  name: string;
}

// App configurations
export const WHOP_APP_CONFIGS: Record<string, WhopAppConfig> = {
  osis: {
    appId: import.meta.env.VITE_WHOP_APP_ID || '',
    agentUserId: import.meta.env.VITE_WHOP_AGENT_USER_ID || '',
    companyId: import.meta.env.VITE_WHOP_COMPANY_ID || '',
    name: 'OSIS'
  },
  trading: {
    appId: import.meta.env.VITE_TRADING_WHOP_APP_ID || '',
    agentUserId: import.meta.env.VITE_TRADING_WHOP_AGENT_USER_ID || '',
    companyId: import.meta.env.VITE_TRADING_WHOP_COMPANY_ID || '',
    name: 'Trading'
  }
};

// Detect current app context
export function detectCurrentApp(): string {
  if (typeof window === 'undefined') {
    return 'osis'; // Default for SSR
  }

  const pathname = window.location.pathname;
  const search = window.location.search;
  const hostname = window.location.hostname;
  const port = window.location.port;
  const fullUrl = window.location.href;

  console.log('🔍 App context detection:', {
    pathname,
    search,
    hostname,
    port,
    fullUrl
  });

  // Check for explicit app parameter first
  if (search.includes('app=trading')) {
    console.log('🎯 Trading app context detected (explicit parameter)');
    return 'trading';
  }

  if (search.includes('app=osis')) {
    console.log('🎯 OSIS app context detected (explicit parameter)');
    return 'osis';
  }

  // Check for experience URLs and extract app type from experience ID
  const experienceMatch = pathname.match(/\/experiences\/(exp_[a-zA-Z0-9]+)/);
  if (experienceMatch) {
    const fullExperienceId = experienceMatch[1]; // e.g., "exp_ThljdpAF70d4Af"
    console.log('🔍 Found experience ID:', fullExperienceId);

    // Try to get experience name for better detection (async, so this is best-effort)
    // The main routing logic will be handled by WhopExperience component
    // This is just for initial app context detection

    // Check if this experience ID corresponds to a trading app (known IDs)
    // You can add specific experience ID mappings here if needed
    if (fullExperienceId === 'exp_ThljdpAF70d4Af') {
      console.log('🎯 Trading app context detected (known experience ID)');
      return 'trading';
    }

    // For experience URLs, default to trading since trade app is only accessed through Whop
    console.log('🎯 Experience URL detected, defaulting to trading (trade app only accessed via Whop)');
    return 'trading';
  }

  // Check for trading app ID patterns in the URL FIRST
  if (
    fullUrl.includes('app_zSMyjWYnc0Et3T') || // Trading app ID
    fullUrl.includes('zSMyjWYnc0Et3T') ||
    hostname.includes('zSMyjWYnc0Et3T')
  ) {
    console.log('🎯 Trading app context detected (app ID pattern)');
    return 'trading';
  }

  // Check for trading-specific indicators
  if (
    pathname.includes('/trade') ||
    pathname.startsWith('/trade') ||
    pathname === '/trade' ||
    pathname.includes('/trading') ||
    pathname.startsWith('/trading') ||
    pathname === '/trading' ||
    fullUrl.includes('trading-') || // Trading app ID pattern like trading-abc123
    fullUrl.includes('trade-') // Trading app ID pattern like trade-ThljdpAF70d4Af
  ) {
    console.log('🎯 Trading app context detected (URL pattern)');
    return 'trading';
  }

  // Check for OSIS app ID patterns in the URL
  if (
    fullUrl.includes('app_VFK6Os0L6NKH0i') || // OSIS app ID
    fullUrl.includes('VFK6Os0L6NKH0i') ||
    hostname.includes('VFK6Os0L6NKH0i') ||
    fullUrl.includes('osis-') // OSIS app ID pattern like osis-cRepYjKZgFSFbL
  ) {
    console.log('🎯 OSIS app context detected (app ID pattern)');
    return 'osis';
  }

  // Special case: localhost:3000 with /trading redirect
  if (port === '3000' && pathname === '/' && hostname === 'localhost') {
    // Check if we're being redirected to trading (this should only happen for trading app)
    // For now, default to OSIS unless explicitly indicated otherwise
    console.log('🎯 Localhost detected - defaulting to OSIS (use ?app=trading to override)');
    return 'osis';
  }

  // Default to trading app since trade app is only accessed through Whop
  console.log('🎯 Trading app context detected (default for Whop access)');
  return 'trading';
}

// Get current app configuration
export function getCurrentAppConfig(): WhopAppConfig {
  const appKey = detectCurrentApp();
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    console.warn(`⚠️ Unknown app key: ${appKey}, falling back to OSIS`);
    return WHOP_APP_CONFIGS.osis;
  }
  
  return config;
}

// Get app configuration by key
export function getAppConfig(appKey: string): WhopAppConfig {
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    console.warn(`⚠️ Unknown app key: ${appKey}, falling back to OSIS`);
    return WHOP_APP_CONFIGS.osis;
  }
  
  return config;
}

// Validate app configuration
export function validateAppConfig(appKey: string): boolean {
  const config = getAppConfig(appKey);
  const requiredFields = ['appId', 'agentUserId', 'companyId'];
  
  const missingFields = requiredFields.filter(field => !config[field as keyof WhopAppConfig]);
  
  if (missingFields.length > 0) {
    console.error(`❌ Missing required fields for ${config.name} app:`, missingFields);
    return false;
  }
  
  return true;
}

// Get intermediary server URL with app parameter
export function getIntermediaryUrl(endpoint: string, appKey?: string): string {
  const baseUrl = import.meta.env.VITE_WHOP_INTERMEDIARY_URL || 'https://whop-intermediary-server.vercel.app';
  const detectedApp = appKey || detectCurrentApp();
  
  const url = new URL(`/api/${endpoint}`, baseUrl);
  url.searchParams.set('app', detectedApp);
  
  return url.toString();
}

// Log configuration status
export function logAppConfigStatus(): void {
  if (typeof window === 'undefined') return;

  const currentApp = detectCurrentApp();
  const currentConfig = getCurrentAppConfig();

  console.log('🔧 Whop App Configuration:', {
    currentApp,
    appName: currentConfig.name,
    appId: currentConfig.appId,
    isValid: validateAppConfig(currentApp),
    allConfigs: Object.keys(WHOP_APP_CONFIGS).map(key => ({
      key,
      name: WHOP_APP_CONFIGS[key].name,
      isValid: validateAppConfig(key)
    }))
  });
}

// Force re-detection of app context (useful after navigation)
export function forceAppContextRedetection(): string {
  if (typeof window === 'undefined') return 'osis';

  console.log('🔄 Forcing app context re-detection...');
  const newApp = detectCurrentApp();
  logAppConfigStatus();
  return newApp;
}

// Initialize app configuration logging (development only)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // Log on page load
  setTimeout(logAppConfigStatus, 1000);

  // Log on route changes
  let lastPathname = window.location.pathname;
  let lastSearch = window.location.search;

  setInterval(() => {
    const currentPathname = window.location.pathname;
    const currentSearch = window.location.search;

    if (currentPathname !== lastPathname || currentSearch !== lastSearch) {
      console.log('🔄 URL changed, re-detecting app context:', {
        from: { pathname: lastPathname, search: lastSearch },
        to: { pathname: currentPathname, search: currentSearch }
      });

      lastPathname = currentPathname;
      lastSearch = currentSearch;
      logAppConfigStatus();
    }
  }, 500); // Check more frequently for redirects
}

/**
 * Enhanced app detection using Whop SDK to get experience app name
 * This is an async version that can fetch experience details
 */
export async function detectCurrentAppWithExperience(): Promise<string> {
  if (typeof window === 'undefined') {
    return 'osis'; // Default for SSR
  }

  const pathname = window.location.pathname;
  const search = window.location.search;
  const fullUrl = window.location.href;

  console.log('🔍 Enhanced app context detection with experience lookup:', {
    pathname,
    search,
    fullUrl
  });

  // Check for explicit app parameter first
  if (search.includes('app=trading')) {
    console.log('🎯 Trading app context detected (explicit parameter)');
    return 'trading';
  }

  if (search.includes('app=osis')) {
    console.log('🎯 OSIS app context detected (explicit parameter)');
    return 'osis';
  }

  // Check for experience URLs and get app name from experience
  const experienceMatch = pathname.match(/\/experiences\/(exp_[a-zA-Z0-9]+)/);
  if (experienceMatch) {
    const experienceId = experienceMatch[1];
    console.log('🔍 Found experience ID, fetching app name:', experienceId);

    // Check cache first
    if (experienceAppCache.has(experienceId)) {
      const cachedAppType = experienceAppCache.get(experienceId)!;
      console.log('🎯 App type from cache:', cachedAppType);
      return cachedAppType;
    }

    try {
      // Use the whop intermediary client to get experience details
      const { whopIntermediaryClient } = await import('@/lib/whopIntermediaryClient');

      // Get experience details via intermediary server
      const response = await whopIntermediaryClient.apiCall(`/experience/${experienceId}`);

      if (response.success && response.data?.experience) {
        const experience = response.data.experience;
        const appName = experience.app?.name?.toLowerCase() || '';
        const experienceName = experience.name?.toLowerCase() || '';
        const detectedApp = experience.detectedApp?.toLowerCase() || '';
        const appKey = experience.appKey || '';

        console.log('✅ Experience info retrieved via intermediary:', {
          experienceId,
          appName,
          experienceName,
          detectedApp,
          appKey
        });

        // Determine app type based on multiple factors
        let appType: string;

        // Priority 1: Use the detected app from the intermediary (most reliable)
        if (detectedApp === 'trading' || appKey === 'trading') {
          appType = 'trading';
        } else if (detectedApp === 'osis' || appKey === 'osis') {
          appType = 'osis';
        }
        // Priority 2: Use app name from experience
        else if (appName.includes('trade') || appName.includes('trading')) {
          appType = 'trading';
        } else if (appName.includes('osis')) {
          appType = 'osis';
        }
        // Priority 3: Fallback to experience name
        else if (experienceName.includes('trade')) {
          appType = 'trading';
        } else if (experienceName.includes('osis') || !experienceName.trim()) {
          appType = 'osis';
        }
        // Priority 4: Final fallback
        else {
          appType = appKey || 'osis';
        }

        // Cache the result
        experienceAppCache.set(experienceId, appType);

        console.log('🎯 App type determined from experience via intermediary:', {
          appType,
          reasoning: detectedApp ? 'detected app' : appName ? 'app name' : experienceName ? 'experience name' : 'fallback'
        });
        return appType;
      } else {
        console.warn('⚠️ No experience data from intermediary, falling back to sync detection');
      }
    } catch (error) {
      console.error('❌ Error fetching experience info via intermediary:', error);
    }
  }

  // Fallback to the original sync detection logic
  return detectCurrentApp();
}

/**
 * Clear the experience app cache
 */
export function clearExperienceAppCache(): void {
  experienceAppCache.clear();
  console.log('🧹 Experience app cache cleared');
}
