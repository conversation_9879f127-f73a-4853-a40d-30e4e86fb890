import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  BarChart3,
  Target,
  Shield,
  Trophy,
  Users,
  Clock,
  CheckCircle,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { useIframeSdk } from '@/hooks/useIframeSdk';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { useToast } from '@/components/ui/use-toast';

interface TradingOnboardingModalProps {
  isOpen: boolean;
  onComplete: () => void;
}

const TradingOnboardingModal: React.FC<TradingOnboardingModalProps> = ({
  isOpen,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const iframeSdk = useIframeSdk();
  const { toast } = useToast();

  const totalSteps = 2;

  const features = [
    {
      icon: <TrendingUp className="h-5 w-5" />,
      title: "Real-Time Paper Trading",
      description: "Practice with live market data without risking real money"
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: "Advanced Charts",
      description: "Professional-grade charts with technical indicators"
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: "Risk Management",
      description: "Set stop losses and manage position sizing"
    },
    {
      icon: <Trophy className="h-5 w-5" />,
      title: "Trading Competition",
      description: "Compete with other traders in live competitions"
    }
  ];

  const competitionFeatures = [
    "🏆 Live trading competition with real prizes",
    "📊 Real-time leaderboard and rankings", 
    "💰 Paper trading with $100,000 virtual portfolio",
    "⏰ 2-week competition duration",
    "🎯 Professional trading environment",
    "📈 Advanced analytics and performance tracking"
  ];

  const handlePayment = async () => {
    if (!iframeSdk) {
      setPaymentError('Payment system not available');
      return;
    }

    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      console.log('💳 Starting Whop payment flow for trading competition...');

      // Create charge via intermediary server (set to $0 as requested)
      const chargeResponse = await whopIntermediaryClient.createCharge(
        0, // $0.00 - free entry as requested
        'usd',
        'Trading Competition Entry - Paper Trading Access'
      );

      console.log('📡 Charge creation response:', chargeResponse);

      if (!chargeResponse.success || !chargeResponse.data?.inAppPurchase) {
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      // Open Whop payment modal
      console.log('🖼️ Opening Whop payment modal...');
      const paymentResult = await iframeSdk.inAppPurchase(chargeResponse.data.inAppPurchase);

      console.log('💳 Payment result:', paymentResult);

      if (paymentResult?.status === "ok") {
        console.log('✅ Payment successful, completing onboarding');
        toast({
          title: "Welcome to the Trading Competition!",
          description: "Your entry has been confirmed. Get ready to trade!",
        });
        onComplete();
      } else {
        throw new Error(paymentResult?.error || 'Payment was not completed');
      }
    } catch (error) {
      console.error('❌ Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      setPaymentError(errorMessage);
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const renderStep1 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-8 relative"
    >
      {/* Background gradient overlay for flair */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.01] via-transparent to-black/10 pointer-events-none rounded-xl" />

      {/* Header with enhanced logo */}
      <div className="relative flex items-center gap-6 mb-12">
        <div className="relative">
          <img
            src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_30_31%20PM.png"
            alt="Logo"
            className="w-24 h-24 object-contain filter drop-shadow-lg"
          />
          {/* Subtle glow effect around logo */}
          <div className="absolute inset-0 w-24 h-24 bg-gradient-to-br from-amber-400/10 to-transparent rounded-full blur-xl" />
        </div>
        <div>
          <h1 className="text-3xl font-normal bg-gradient-to-r from-white via-white/90 to-white/70 bg-clip-text text-transparent leading-tight mb-1"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif', letterSpacing: '-0.02em' }}>
            Welcome to the Trading
          </h1>
          <h2 className="text-3xl font-normal bg-gradient-to-r from-white via-white/90 to-white/70 bg-clip-text text-transparent leading-tight"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif', letterSpacing: '-0.02em' }}>
            Competition!
          </h2>
        </div>
      </div>

      {/* Search Interface Preview - Centered */}
      <div className="relative flex justify-center mb-8">
        <div className="relative max-w-lg w-full">
          {/* Multiple shadow layers for depth */}
          <div className="absolute -inset-4 bg-gradient-to-br from-black/40 via-black/60 to-black/80 rounded-3xl blur-2xl" />
          <div className="absolute -inset-2 bg-gradient-to-br from-black/30 via-black/50 to-black/70 rounded-2xl blur-xl" />
          <div className="absolute -inset-1 bg-black/40 rounded-xl blur-lg" />

          {/* Image container with corner shadows */}
          <div className="relative bg-gradient-to-br from-black/20 via-transparent to-black/40 rounded-2xl p-1">
            <div className="relative rounded-xl overflow-hidden">
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/Screenshot%202025-07-10%20at%208.56.00%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9TY3JlZW5zaG90IDIwMjUtMDctMTAgYXQgOC41Ni4wMCBQTS5wbmciLCJpYXQiOjE3NTIxOTUzNzEsImV4cCI6MTc4MzczMTM3MX0.hE38XcPGcrKFXqoAyP9SIZ59eS39fHoXwBL0FTnad60"
                alt="Trading Competition Search Interface"
                className="w-full h-auto object-cover rounded-xl"
              />

              {/* Corner shadow overlays for seamless integration */}
              <div className="absolute top-0 left-0 w-6 h-6 bg-gradient-to-br from-black/60 to-transparent rounded-tl-xl" />
              <div className="absolute top-0 right-0 w-6 h-6 bg-gradient-to-bl from-black/60 to-transparent rounded-tr-xl" />
              <div className="absolute bottom-0 left-0 w-6 h-6 bg-gradient-to-tr from-black/60 to-transparent rounded-bl-xl" />
              <div className="absolute bottom-0 right-0 w-6 h-6 bg-gradient-to-tl from-black/60 to-transparent rounded-br-xl" />
            </div>
          </div>

          {/* Floating elements for extra flair */}
          <div className="absolute -top-2 -right-2 w-2 h-2 bg-amber-400/30 rounded-full blur-sm animate-pulse" />
          <div className="absolute -bottom-3 -left-3 w-1.5 h-1.5 bg-white/20 rounded-full blur-sm animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
      </div>

      {/* Features with enhanced styling */}
      <div className="relative space-y-6">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-4 p-3 rounded-lg bg-white/[0.01] border border-white/[0.05] hover:bg-white/[0.02] transition-all duration-200">
            <div className="w-2 h-2 bg-gradient-to-r from-amber-400/60 to-white/60 rounded-full flex-shrink-0 mt-3" />
            <div>
              <h3 className="font-medium text-white mb-1 text-base"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                {feature.title}
              </h3>
              <p className="text-white/70 text-sm leading-relaxed">{feature.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Get Started Button */}
      <div className="relative flex justify-center pt-8">
        <button
          onClick={nextStep}
          className="group relative bg-white text-black font-semibold py-4 px-12 text-lg rounded-xl transition-all duration-300 hover:bg-white/95 hover:scale-[1.02] active:scale-[0.98] shadow-[inset_0_2px_8px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_2px_12px_rgba(0,0,0,0.15),0_8px_32px_rgba(255,255,255,0.1)]"
          style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}
        >
          <span className="relative z-10">Get Started</span>
          {/* Subtle inner glow */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/20 via-transparent to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </button>

        {/* Floating description */}
        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-center">
          <p className="text-white/40 text-sm">
            Join the competition and start trading
          </p>
        </div>
      </div>
    </motion.div>
  );

  const renderStep2 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-8"
    >
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-normal text-white mb-4">
          Join the Competition
        </h2>
        <p className="text-white/70 text-base">
          Secure your spot in our exclusive trading competition
        </p>
      </div>

      {/* Competition Details */}
      <div className="bg-white/5 border border-white/10 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-normal text-white">Competition Entry</h3>
          <span className="bg-white/10 text-white px-3 py-1 rounded-full text-sm border border-white/20">
            FREE
          </span>
        </div>

        <div className="space-y-4">
          {competitionFeatures.map((feature, index) => (
            <div key={index} className="flex items-start gap-4">
              <div className="w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2" />
              <span className="text-white/80 text-sm leading-relaxed">{feature}</span>
            </div>
          ))}
        </div>

        <div className="pt-6 border-t border-white/10 mt-6">
          <div className="flex items-center justify-between">
            <span className="text-white/60 text-sm">Entry Fee:</span>
            <span className="text-xl font-normal text-white">FREE</span>
          </div>
        </div>
      </div>

      {paymentError && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400 text-sm">{paymentError}</p>
        </div>
      )}

      <div className="flex justify-center pt-8">
        <button
          onClick={handlePayment}
          disabled={isProcessingPayment}
          className="bg-white/5 hover:bg-white/10 text-white border border-white/10 px-8 py-3 text-base rounded-lg transition-all duration-300 backdrop-blur-sm disabled:opacity-50"
        >
          {isProcessingPayment ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            'Join Competition'
          )}
        </button>
      </div>
    </motion.div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-2xl bg-black border border-white/10 backdrop-blur-xl text-white"
        hideCloseButton
      >
        <div className="p-8">
          <AnimatePresence mode="wait">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
          </AnimatePresence>

          {/* Progress indicators */}
          <div className="flex justify-center gap-2 mt-8">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div
                key={i}
                className={`h-2 w-8 rounded-full transition-colors ${
                  i + 1 <= currentStep ? 'bg-white/40' : 'bg-white/20'
                }`}
              />
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TradingOnboardingModal;
