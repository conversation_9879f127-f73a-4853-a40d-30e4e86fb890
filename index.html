<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Permissions-Policy" content="ch-dpr=(), ch-width=(), ch-viewport-width=(), ch-device-memory=(), ch-ua=(), ch-ua-arch=(), ch-ua-model=(), ch-ua-platform=(), ch-ua-platform-version=(), ch-ua-mobile=(), ch-ua-full-version=(), ch-ua-full-version-list=(), ch-ua-bitness=(), ch-ua-wow64=(), ch-prefers-color-scheme=(), ch-prefers-reduced-motion=(), ch-save-data=(), ch-downlink=(), ch-ect=(), ch-rtt=()">
    <title>Trade App</title>
    <meta name="description" content="Professional trading platform with real-time market data, advanced charting, and paper trading capabilities. Trade stocks with confidence using our comprehensive trading tools." />
    <meta name="author" content="Trade App" />

    <!-- PostHog Analytics -->
    <script>
      !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init Ie Ts Ms Ee Es Rs capture Ge calculateEventProperties Os register register_once register_for_session unregister unregister_for_session js getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Ds Fs createPersonProfile Ls Ps opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Cs debug I As getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
      posthog.init('phc_svkkrbxrYi4ckp5G1CmJuVUetTARRYLSFPd4Mbtw8Mz', {
          api_host: 'https://us.i.posthog.com',
          defaults: '2025-05-24',
          person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
      })
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- Content script error prevention -->
    <script src="/content-script-fix.js"></script>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
